@import "tailwindcss";

// @import "tw-animate-css";

// @custom-variant dark (&:is(.dark *));

@font-face {
  font-family: "outfit";
  src: url("/fonts/outfit.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

:root {
  --head: rgb(25, 32, 36);
  --parawhite: rgb(255, 255, 255, 0.7);
  --para: rgb(0, 0, 0, 0.7);
  --white: #ffffff;
  --black: #171717;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}


* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  background: var(--white);
  color: var(--black);
  font-family: "outfit", sans-serif;
  font-optical-sizing: auto;
}

.pageContainer{
  width: 99.65vw !important;
  overflow-x: hidden;
}

@media (max-width: 768px) {
  .pageContainer {
    margin-top: 45px; /* Adjust for smaller slider height */
  }
}

@media (max-width: 480px) {
  .pageContainer {
    margin-top: 40px; /* Adjust for smallest slider height */
  }
}

::-webkit-scrollbar {
  width: 5px;
  border-radius: 5rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: var(--lightblue);
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #b5b4b4;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #888;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

input:focus,
textarea:focus {
  outline: none;
}

.carouselbtn,
button,
img,
svg {
  cursor: pointer !important;
}

span {
  color: var(--para);
}

h1,
h2,
h3 {
  color: var(--head);
}

p {
  color: var(--para);
}

img,
svg {
  transition: 0.2s all;
}

img:hover,
svg:hover {
  transform: scale(1.05);
}

.imgContainer {
  overflow: hidden;
}