// Color Variables
$primary-blue: #0066ff;
$secondary-blue: #3385ff;
$light-blue: #e6f0ff;
$text-dark: #000;
$text-gray: #333;
$white: #ffffff;
$gradient-blue: linear-gradient(135deg, $primary-blue, $secondary-blue);

.hero-section {
  background: $white;
  min-height: 100vh;
  padding: 2rem 0;
  position: relative;
  overflow: hidden;

  .hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
  }

  .hero-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 70vh;
    padding: 4rem 0;
  }

  .hero-title {
    font-size: 4rem;
    font-weight: 800;
    color: $text-dark;
    text-align: center;
    margin-bottom: 4rem;
    line-height: 1.2;
    max-width: 800px;

    .text-blue {
      color: $primary-blue;
    }

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }

  .hero-left {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    max-width: 300px;

    .hero-subtext {
      color: $text-gray;
      font-size: 1rem;
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .chat-button {
      background: $gradient-blue;
      color: $white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 25px;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba($primary-blue, 0.3);
      }

      .arrow {
        font-size: 1.2rem;
      }
    }

    @media (max-width: 1024px) {
      position: static;
      transform: none;
      margin-bottom: 2rem;
      text-align: center;
      max-width: none;
    }
  }

  .hero-right {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    max-width: 300px;
    text-align: right;

    .hero-subtext {
      color: $text-gray;
      font-size: 1rem;
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .appointment-button {
      background: transparent;
      border: 2px solid $primary-blue;
      color: $primary-blue;
      padding: 0.75rem 1rem;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;

      &:hover {
        background: $primary-blue;
        color: $white;
        transform: translateY(-2px);
      }

      .arrow {
        font-size: 1.2rem;
      }
    }

    @media (max-width: 1024px) {
      position: static;
      transform: none;
      margin-bottom: 2rem;
      text-align: center;
      max-width: none;
    }
  }

  .hero-figure {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2rem 0;

    .figure-placeholder {
      width: 400px;
      height: 400px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .running-figure {
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><defs><linearGradient id="muscleGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23ff6b6b;stop-opacity:0.8" /><stop offset="100%" style="stop-color:%23ee5a24;stop-opacity:0.9" /></linearGradient></defs><g transform="translate(200,200)"><ellipse cx="0" cy="150" rx="60" ry="20" fill="%23ddd" opacity="0.3"/><g transform="rotate(-10)"><rect x="-15" y="-180" width="30" height="40" rx="15" fill="%23fdbcb4"/><rect x="-20" y="-150" width="40" height="60" rx="20" fill="url(%23muscleGrad)"/><rect x="-25" y="-100" width="50" height="80" rx="25" fill="url(%23muscleGrad)"/><rect x="-15" y="-30" width="30" height="60" rx="15" fill="url(%23muscleGrad)"/><rect x="-12" y="20" width="24" height="50" rx="12" fill="url(%23muscleGrad)"/><rect x="-8" y="60" width="16" height="30" rx="8" fill="%23fdbcb4"/><g transform="translate(-30,-120) rotate(-20)"><rect x="-8" y="0" width="16" height="50" rx="8" fill="url(%23muscleGrad)"/><rect x="-6" y="40" width="12" height="30" rx="6" fill="url(%23muscleGrad)"/><rect x="-4" y="65" width="8" height="15" rx="4" fill="%23fdbcb4"/></g><g transform="translate(30,-120) rotate(45)"><rect x="-8" y="0" width="16" height="50" rx="8" fill="url(%23muscleGrad)"/><rect x="-6" y="40" width="12" height="30" rx="6" fill="url(%23muscleGrad)"/><rect x="-4" y="65" width="8" height="15" rx="4" fill="%23fdbcb4"/></g><g transform="translate(-20,30) rotate(-30)"><rect x="-10" y="0" width="20" height="60" rx="10" fill="url(%23muscleGrad)"/><rect x="-8" y="50" width="16" height="40" rx="8" fill="url(%23muscleGrad)"/><rect x="-6" y="85" width="12" height="20" rx="6" fill="%23fdbcb4"/></g><g transform="translate(25,30) rotate(60)"><rect x="-10" y="0" width="20" height="60" rx="10" fill="url(%23muscleGrad)"/><rect x="-8" y="50" width="16" height="40" rx="8" fill="url(%23muscleGrad)"/><rect x="-6" y="85" width="12" height="20" rx="6" fill="%23fdbcb4"/></g></g></g></svg>') center/contain no-repeat;
        filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
      }

      @media (max-width: 768px) {
        width: 300px;
        height: 300px;
      }
    }
  }

  .info-cards {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2rem;
    margin-top: 4rem;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .info-card {
    background: $gradient-blue;
    border-radius: 20px;
    padding: 2rem;
    color: $white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba($primary-blue, 0.2);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
      pointer-events: none;
    }
  }

  .users-card {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    .card-header {
      .card-title {
        font-size: 3rem;
        font-weight: 800;
        margin: 0;
      }

      .card-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
        letter-spacing: 1px;
      }
    }

    .services-section {
      .services-title {
        font-size: 0.9rem;
        font-weight: 600;
        margin: 0 0 1rem 0;
        letter-spacing: 1px;
      }

      .services-description {
        font-size: 0.9rem;
        line-height: 1.5;
        opacity: 0.9;
        margin-bottom: 1.5rem;
      }

      .more-button {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: $white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        .arrow {
          margin-left: 0.5rem;
        }
      }
    }

    .doctors-section {
      display: flex;
      align-items: center;
      gap: 1rem;

      .doctor-avatars {
        display: flex;
        gap: -0.5rem;

        .avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: 2px solid $white;
          margin-left: -0.5rem;

          &.avatar-1 {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
          }

          &.avatar-2 {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
          }

          &.avatar-3 {
            background: linear-gradient(45deg, #45b7d1, #96c93d);
          }

          &:first-child {
            margin-left: 0;
          }
        }
      }

      .doctors-count {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
      }
    }
  }

  .treatments-card {
    .card-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
      letter-spacing: 1px;
    }

    .card-description {
      font-size: 0.9rem;
      line-height: 1.5;
      opacity: 0.9;
    }
  }

  .online-care-card {
    .care-icon {
      margin-bottom: 1.5rem;

      .icon-circles {
        display: flex;
        gap: 0.5rem;

        .circle {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.8);

          &.circle-2 {
            opacity: 0.6;
          }

          &.circle-3 {
            opacity: 0.4;
          }
        }
      }
    }

    .card-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
      letter-spacing: 1px;
    }

    .card-description {
      font-size: 0.9rem;
      line-height: 1.5;
      opacity: 0.9;
      margin-bottom: 1.5rem;
    }

    .contact-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: $white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .arrow {
        margin-left: 0.5rem;
      }
    }
  }
}
